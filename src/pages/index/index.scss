.index {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: #f5f5f5;
	display: flex;
	flex-direction: column;

	// 设置 NutUI 组件的 CSS 变量
	--nut-picker-confirm-color: #397fef;
	--nut-primary-color: #397fef;

	// 导航栏左侧按钮样式
	.left_btn {
		font-weight: 600;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		z-index: 999;
		color: #333;
		font-size: 36px;
		.nut-icon {
			// 如何设置图标和文字垂直居中
			line-height: 1;
			margin-top: 2px;
			color: #333;
		}
	}

	// 固定的轮播图容器
	.swiper-container {
		flex-shrink: 0; // 不允许压缩
		position: relative;
		z-index: 1;
		padding: 20px;
    border-radius: 5px;
		// 禁用 NutSwiper 的滚动
		:deep(.nut-swiper) {
			touch-action: pan-x; // 只允许水平滑动
			overflow: hidden;
		}

		// 确保轮播图不会影响页面滚动
		:deep(.nut-swiper-wrapper) {
			touch-action: pan-x;
		}
	}

	// 可滚动的页面内容区域
	.content {
		flex: 1; // 占据剩余空间
		overflow-y: auto; // 允许垂直滚动
		padding: 20px;
		box-sizing: border-box;
		background-color: #f5f5f5;

		.content-item {
			background-color: #fff;
			margin-bottom: 20px;
			padding: 30px;
			border-radius: 12px;
			box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

			text {
				font-size: 32px;
				color: #333;
			}
		}
	}

	// 浮动按钮
	.door_btn {
		position: fixed;
		right: 30px;
		bottom: 50px;
		width: 100px;
		height: 100px;
		border-radius: 50%;
		background-color: #397fef;
		box-shadow: 10px 10px 20px rgba(180, 180, 180, 0.8);
		display: flex;
		align-items: center;
		z-index: 1000;

		.door_btn_image {
			width: 50%;
			margin: auto;
		}
	}

	// NutPicker 组件样式自定义
	:deep(.nut-picker) {
		// 确认按钮样式
		.nut-picker__confirm-btn {
			color: #397fef !important;
		}

		// 如果需要修改按钮背景色（当按钮有背景时）
		.nut-picker__toolbar .nut-picker__confirm {
			color: #397fef !important;
		}

		// 修改底部操作栏确认按钮
		.nut-picker__bottom .nut-picker__confirm,
		.nut-picker__footer .nut-picker__confirm {
			color: #397fef !important;
		}
	}

	// 如果 Picker 在 Popup 中，也需要覆盖 Popup 内的样式
	:deep(.nut-popup) {
		.nut-picker__confirm-btn,
		.nut-picker__confirm {
			color: #397fef !important;
		}
	}
}
