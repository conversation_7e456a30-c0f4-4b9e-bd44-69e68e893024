<template>
	<view class="index">
		<!-- 自定义导航栏 -->
		<CustomNavBar>
			<template #left>
				<view
					class="left_btn"
					:style="{ width: `${getNavBarContentWidth()}px` }"
					@click="showAreaList = true"
				>
					<TriangleDown size="10" />
					{{ areaName }}
				</view>
				<!-- 区域选择 -->
				<NutPopup v-model:visible="showAreaList" position="bottom">
					<NutPicker
						v-model="areaId"
						:columns="areaList"
						title="请选择区域"
						@confirm="confirm"
						@cancel="showAreaList = false"
					/>
				</NutPopup>
			</template>
		</CustomNavBar>

		<!-- 固定的轮播图区域 -->
		<view class="swiper-container" :style="{ marginTop: `${getNavBarHeight()}px` }">
			<NutSwiper
				:init-page="1"
				:auto-play="3000"
				pagination-visible
				pagination-color="#426543"
				pagination-unselected-color="#808080"
			>
				<NutSwiperItem
					v-for="(item, index) in swiperList"
					:key="index"
					:style="{ height: `${getHeightByRatio(3)}px` }"
				>
					<img
						:src="item"
						alt=""
						style="height: 100%; width: 100%; border-radius: 5px"
						draggable="false"
					/>
				</NutSwiperItem>
			</NutSwiper>
		</view>

		<!-- 可滚动的页面内容区域 -->
		<view class="content">
			<view class="content-item" v-for="i in 20" :key="i">
				<text>内容项 {{ i }}</text>
			</view>
		</view>

		<!-- 浮动按钮 -->
		<view class="door_btn" @click="onClick">
			<image class="door_btn_image" src="@/assets/images/index_door.png" mode="widthFix" />
		</view>

		<nut-toast v-model:visible="show" :msg="msg" />
	</view>

	<CustomModal
		:is-phone-auth="true"
		v-model:visible="modalVisible"
		:title="modalConfig.title"
		:content="modalConfig.content"
		:confirm-text="modalConfig.confirmText"
		:cancel-text="modalConfig.cancelText"
		@confirm="onConfirm"
	/>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import './index.scss';
import { useUserStore } from '@/stores';
import { useLifecycle } from '@/hooks/useLifecycle';
import { getNavBarHeight, getNavBarContentWidth, getHeightByRatio } from '@/utils/screen';
import { demoTestCounterStore } from '@/stores';
import { onLoginHandler } from '@/utils/login';
// 引入图标组件
import { TriangleDown } from '@nutui/icons-vue-taro';
const userStore = useUserStore();
const isLogin = computed(() => userStore.isLogin());

// 在这里监听watch的时候重新渲染界面即可
watch(isLogin, (newValue, oldValue) => {
	console.log('isLogin 变化了:', newValue);
	if (newValue && !oldValue) {
		// 用户刚刚登录成功
		console.log('用户登录成功，【首页】刷新页面数据');
		// 可以在这里执行需要登录后才能获取的数据
		// 比如：获取用户专属内容、刷新权限相关的UI等
	} else if (!newValue && oldValue) {
		// 用户刚刚退出登录
		console.log('用户退出登录，【首页】清理页面数据');
		// 清理需要登录才能显示的数据
	}
});

onMounted(() => {
	console.log('mounted 首页初始化：', isLogin.value);
});

const show = ref(false);
const msg = ref('');
const counter = demoTestCounterStore();

const areaName = ref('选择区域');
const areaId = ref(['communityId_1']);

const areaList = ref([
	{
		text: '新乡产业园人才公寓楼 1幢',
		value: 'communityId_1',
	},
	{
		text: '新乡产业园人才公寓楼 2幢',
		value: 'communityId_2',
	},
	{
		text: '新乡产业园人才公寓楼 3幢',
		value: 'communityId_3',
	},
]);

const showAreaList = ref(false);

const confirm = ({ selectedOptions, selectedValue }) => {
	areaId.value = [selectedValue[0]];
	areaName.value = selectedOptions[0].text;
	showAreaList.value = false;
};

const swiperList = ref([
	'https://resources.fanjianhome.com/Fia-R8qEShGzjUgaqeV-qZqgvKB4?imageslim',
	'https://resources.fanjianhome.com/Fia-R8qEShGzjUgaqeV-qZqgvKB4?imageslim',
	'https://resources.fanjianhome.com/Fia-R8qEShGzjUgaqeV-qZqgvKB4?imageslim',
]);

// 使用生命周期 Hook
const { pageState, isLoading, setLoading } = useLifecycle(
	{
		onLoad: async (options) => {
			console.log('首页页面加载，参数:', options);
			// 模拟数据加载
			await new Promise((resolve) => setTimeout(resolve, 1000));
			console.log('首页数据加载完成');
		},
		onShow: async () => {
			console.log('首页页面显示');
			// 可以在这里刷新数据
		},
		onHide: () => {
			console.log('首页页面隐藏');
		},
	},
	{
		autoLoading: true,
		enableAnalytics: true,
	},
);

console.log(
	'声明周期hook参数：',
	'pageState:',
	pageState,
	'isLoading:',
	isLoading,
	'setLoading:',
	setLoading,
);

const onClick = () => {
	if (!isLogin.value) {
		modalVisible.value = true;
		return;
	}
	show.value = true;
	counter.count++;
	msg.value = `你成功了:${counter.count}次`;
};

// ========== 自定义 Modal 相关方法 ==========
const modalVisible = ref(false);
const modalConfig = ref({
	title: '手机号快速登录',
	content: '使用微信手机号进行快速登录，享受更好的服务体验',
	confirmText: '立即授权',
	cancelText: '取消',
});

// 处理确认逻辑
const onConfirm = (e) => {
	onLoginHandler(e, '/pages/index/index');
};
</script>
