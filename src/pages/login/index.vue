<template>
	<view class="login-container">
		<view class="login-header">
			<text class="login-title">员工登录</text>
		</view>

		<view class="login-form">
			<view class="form-item">
				<NutInput
					v-model="loginForm.username"
					placeholder="请输入登录名"
					:border="false"
					class="login-input"
				/>
			</view>

			<view class="form-item">
				<NutInput
					v-model="loginForm.password"
					type="password"
					placeholder="请输入登录密码"
					:border="false"
					class="login-input"
				/>
			</view>

			<view class="login-btn-container">
				<NutButton
					type="primary"
					size="large"
					:loading="isLoading"
					@click="handleLogin"
					class="login-btn"
				>
					登录
				</NutButton>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import Taro from '@tarojs/taro';
import './index.scss';
import { useAuthStore, useUserStore } from '@/stores';
import { saveStore } from '@/utils/utils';

// 登录表单数据
const loginForm = reactive({
	username: '',
	password: ''
});

const isLoading = ref(false);

// 模拟登录处理函数
const handleLogin = async () => {
	// 基本验证
	if (!loginForm.username.trim()) {
		Taro.showToast({
			title: '请输入登录名',
			icon: 'none'
		});
		return;
	}

	if (!loginForm.password.trim()) {
		Taro.showToast({
			title: '请输入登录密码',
			icon: 'none'
		});
		return;
	}

	isLoading.value = true;

	try {
		// 模拟登录请求
		await new Promise(resolve => setTimeout(resolve, 1000));

		// 模拟登录成功，设置用户信息
		const mockToken = 'Bearer mock-token-' + Date.now();
		const mockUserInfo = {
			id: 1,
			username: loginForm.username,
			name: '员工姓名',
			avatar: '',
			phone: '138****8888'
		};

		// 存储到store和本地存储
		const authStore = useAuthStore();
		const userStore = useUserStore();

		authStore.setToken(mockToken);
		userStore.setUserInfo(mockUserInfo);

		saveStore('token', mockToken);
		saveStore('userInfo', mockUserInfo);

		Taro.showToast({
			title: '登录成功',
			icon: 'success'
		});

		// 跳转到首页
		setTimeout(() => {
			Taro.switchTab({
				url: '/pages/index/index'
			});
		}, 1000);

	} catch (error) {
		console.error('登录失败:', error);
		Taro.showToast({
			title: '登录失败，请重试',
			icon: 'none'
		});
	} finally {
		isLoading.value = false;
	}
};
</script>
