.login-container {
	min-height: 100vh;
	background: #f8f9fa;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 40px 30px;
	box-sizing: border-box;
}

.login-header {
	margin-bottom: 60px;
	text-align: center;
}

.login-title {
	font-size: 32px;
	font-weight: bold;
	color: #333333;
	text-shadow: none;
}

.login-form {
	width: 100%;
	max-width: 360px;
	background: #ffffff;
	border-radius: 12px;
	padding: 40px 30px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
	border: 1px solid #e9ecef;
}

.form-item {
	margin-bottom: 24px;

	&:last-child {
		margin-bottom: 0;
	}
}

.login-input {
	width: 100%;
	height: 50px;
	background: #f8f9fa;
	border-radius: 8px;
	padding: 0 16px;
	font-size: 16px;
	border: 1px solid #e9ecef;
	transition: all 0.3s ease;

	&:focus {
		border-color: #397fef;
		background: #ffffff;
		box-shadow: 0 0 0 3px rgba(57, 127, 239, 0.1);
	}
}

.login-btn-container {
	margin-top: 32px;
}

.login-btn {
	width: 100%;
	height: 50px;
	background: #397fef;
	border: none;
	border-radius: 8px;
	font-size: 16px;
	font-weight: 600;
	color: #ffffff;
	transition: all 0.3s ease;

	&:hover {
		background: #2968d8;
	}

	&:active {
		transform: translateY(1px);
		box-shadow: 0 2px 8px rgba(57, 127, 239, 0.3);
	}

	&.nut-button--loading {
		opacity: 0.8;
	}
}

// 覆盖NutUI组件样式
:deep(.nut-input) {
	background: transparent !important;
	border: none !important;

	.nut-input-value {
		background: transparent !important;
		border: none !important;
		font-size: 16px !important;
		color: #333333 !important;

		&::placeholder {
			color: #999999 !important;
		}
	}
}

:deep(.nut-button) {
	border: none !important;

	&.nut-button--primary {
		background: #397fef !important;

		&:hover {
			background: #2968d8 !important;
		}
	}
}
