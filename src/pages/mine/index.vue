<template>
	<view class="mine">
		<!-- 渐变背景头部区域 -->
		<view class="gradient-header">
			<view class="custom-nav-bar" :style="{ height: `${getNavBarHeight()}px` }">
				<view
					class="center-title"
					:style="{ marginTop: `${getNavBarHeight() - getMenuHeight()}px` }"
				>
					个人中心
				</view>
			</view>
			<!-- 用户信息卡片 -->
			<view class="user-card">
				<view class="user-avatar">
					<!-- <image class="avatar-img" :src="avatarUrl" mode="widthFix" /> -->
					<image
						:src="avatarUrl"
						class="avatar-img"
						mode="widthFix"
						@error="onAvatarError"
					/>
				</view>
				<button
					v-if="!isLogin"
					class="text-button"
					open-type="getPhoneNumber"
					@getPhoneNumber="onLoginHandler"
				>
					<view class="button-text">登录 / 注册</view>
				</button>
				<!-- 类似Flutter 中的 SizedBox 自身无大小，全靠内容撑开，可以用于包裹内容 + 判断 -->
				<template v-if="isLogin">
					<view class="user-info">
						<text class="user-name">{{ userInfo.nickname || '匿名用户' }}</text>
						<!-- 手机号需要隐藏中间四位 -->
						<text class="user-phone">
							{{
								userInfo.mobile
									? userInfo.mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
									: ''
							}}
						</text>
					</view>
					<view
						class="user-id-card"
						v-if="!userInfo.idCardNo"
						@click="onTrueNameAuthHandler"
					>
						未实名，立即认证
					</view>
					<view class="arrow-icon" @click="onProfileDetailHandler">
						<RectRight color="white" />
					</view>
				</template>
			</view>
		</view>
		<view class="line-card">
			<view
				class="line-card-item"
				v-for="(item, index) in myFuncList"
				:key="index"
				@click="onFuncClick(item)"
			>
				<image class="line-card-item-icon" :src="item.icon" />
				<view class="line-card-item-text">
					<text>{{ item.title }}</text>
				</view>
			</view>
		</view>

		<!-- 其他内容区域 -->
		<view class="mine-content">
			<view class="body-card">
				<view class="body-card-title">我的公寓</view>
				<view class="body-card-content">
					<view
						class="body-card-cell"
						v-for="(item, index) in myApartmentList"
						:key="index"
						@click="onCellHandler(item)"
					>
						<view class="body-card-cell-left">
							<image :src="item.icon" class="body-card-cell-left-img" />
							<text>{{ item.title }}</text>
						</view>
						<RectRight size="12" />
					</view>
				</view>
			</view>
			<!-- 退出登录 -->
			<nut-button
				v-if="isLogin"
				type="primary"
				style="margin: 0px 30px; width: calc(100% - 60px)"
				@click="onLogoutHandler"
			>
				退出登录
			</nut-button>

			<!-- 测试按钮 -->
			<!-- <Button open-type="getPhoneNumber" @getPhoneNumber="onSupportSoterAuthenticationHandler"
        class="taro-button-plain">
        生物认证
      </Button>
      <Button open-type="chooseAvatar" @chooseAvatar="onChooseAvatar" class="taro-button-plain">
        微信头像
      </Button> -->
		</view>
	</view>

	<CustomModal
		:is-phone-auth="true"
		v-model:visible="modalVisible"
		:title="modalConfig.title"
		:content="modalConfig.content"
		:confirm-text="modalConfig.confirmText"
		:cancel-text="modalConfig.cancelText"
		@confirm="onConfirm"
	/>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import './index.scss';
import { getNavBarHeight, getMenuHeight } from '@/utils/screen';
import Taro from '@tarojs/taro';
import { onLoginHandler, onLogoutHandler } from '@/utils/login';
import { useUserStore } from '@/stores';
import { RectRight } from '@nutui/icons-vue-taro';
import MineOrderPNG from '@/assets/images/mine_order.png';
import MineContractPNG from '@/assets/images/mine_contract.png';
import MineBillPNG from '@/assets/images/mine_bill.png';
import MineQuotaPNG from '@/assets/images/mine_quota.png';
import MineRepairPNG from '@/assets/images/mine_repair.png';
import MineLockPNG from '@/assets/images/mine_lock.png';
import MineWaterElePNG from '@/assets/images/mine_water_ele.png';
import MineSharePNG from '@/assets/images/mine_share.png';
import DefaultAvatar from '@/assets/images/mine_avatar.png';
import { getFullAvatarUrl } from '@/utils/utils';

const userStore = useUserStore();
const isLogin = computed(() => userStore.isLogin());
const userInfo = computed(() => userStore.getUserInfo());
// 深度遍历，监听 userInfo 的变化
watch(
	userInfo,
	(newValue, oldValue) => {
		console.log('userInfo 变化:', newValue, oldValue);
		if (isLogin.value) {
			avatarUrl.value = getFullAvatarUrl(newValue?.profilePicture || '') || DefaultAvatar;
		} else {
			avatarUrl.value = DefaultAvatar;
		}
	},
	{ deep: true },
);
console.log('isLogin:', userInfo.value);

const onTrueNameAuthHandler = () => {
	// 跳转到实名认证页面
	Taro.navigateTo({
		url: '/pagesA/identityAuth/index',
	});
};

// 生物认证
// const onSupportSoterAuthenticationHandler = async () => {
//   console.log('onSupportSoterAuthenticationHandler');
//   const result = await Taro.checkIsSupportSoterAuthentication();
//   console.log('result:', result);
// };
// getFullAvatarUrl(userInfo.value?.profilePicture || '') ||
const avatarUrl = ref(
	isLogin.value ? getFullAvatarUrl(userInfo.value?.profilePicture || '') : DefaultAvatar,
);
// 图片加载失败时的回调
const onAvatarError = (_e) => {
	avatarUrl.value = DefaultAvatar;
};

// 用户详情
const onProfileDetailHandler = () => {
	Taro.navigateTo({
		url: '/pagesA/profile/index',
	});
};

// 功能列表
const myFuncList = [
	{
		title: '约看',
		icon: MineOrderPNG,
		path: '',
	},
	{
		title: '合同',
		icon: MineContractPNG,
		path: '',
	},
	{
		title: '账单',
		icon: MineBillPNG,
		path: '',
	},
	{
		title: '额度',
		icon: MineQuotaPNG,
		path: '',
	},
];
const onFuncClick = (item) => {
	if (!isLogin.value) {
		modalVisible.value = true;
		return;
	}
	Taro.showToast({
		title: `${item.title}功能正在开发中...`,
		icon: 'none',
	});
};

// 我的公寓
const myApartmentList = [
	{
		title: '报事报修',
		icon: MineRepairPNG,
		path: '',
	},
	{
		title: '智能门锁',
		icon: MineLockPNG,
		path: '',
	},
	{
		title: '水电能源',
		icon: MineWaterElePNG,
		path: '',
	},
	{
		title: '分享',
		icon: MineSharePNG,
		path: '',
	},
];
const onCellHandler = (item) => {
	if (!isLogin.value) {
		modalVisible.value = true;
		return;
	}
	Taro.showToast({
		title: `${item.title}功能正在开发中...`,
		icon: 'none',
	});
};

// ========== 自定义 Modal 相关方法 ==========
const modalVisible = ref(false);
const modalConfig = ref({
	title: '手机号快速登录',
	content: '使用微信手机号进行快速登录，享受更好的服务体验',
	confirmText: '立即授权',
	cancelText: '取消',
});

const onConfirm = (e) => {
	console.log('点击了确定:', e);
	// 处理确认逻辑
	onLoginHandler(e);
};
</script>
