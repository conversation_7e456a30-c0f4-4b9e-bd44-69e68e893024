import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { setupRequest } from '@/libs/request-setup';
import { initStore } from '@/stores';
import { IconFont } from '@nutui/icons-vue-taro';
import './app.scss';

const App = createApp({
	onLaunch(options) {
		console.log('🚀 应用启动', options);

		// 从本地LocalStorage读取初始化pinia中的数据 token和userInfo
		initStore();
		// 初始化请求库
		setupRequest();
	},
	onShow(options) {
		console.log('📱 应用显示', options);
	},
	onHide() {
		console.log('📱 应用隐藏');
	},
	// 入口组件不需要实现 render 方法，即使实现了也会被 taro 所覆盖
});

App.use(createPinia()).component('IconFont', IconFont);

export default App;
