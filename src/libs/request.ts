/**
 * 网络请求库
 * 基于 Taro.request 封装，支持多端、拦截器、错误处理等
 */

import Taro from '@tarojs/taro';
import { useAuthStore } from '@/stores';

// 请求配置类型
export interface RequestConfig extends Taro.request.Option {
	// 是否显示加载提示
	showLoading?: boolean;
	// 加载提示文字
	loadingText?: string;
	// 是否显示错误提示
	showError?: boolean;
	// 是否需要认证
	needAuth?: boolean;
	// 重试次数
	retry?: number;
	// 超时时间
	timeout?: number;
}

// 响应数据类型
export interface ResponseData<T = any> {
	code: number;
	message: string;
	data: T;
	success: boolean;
}

// 请求拦截器类型
export type RequestInterceptor = (config: RequestConfig) => RequestConfig | Promise<RequestConfig>;

// 响应拦截器类型
export type ResponseInterceptor = (response: any) => any | Promise<any>;

// 错误拦截器类型
export type ErrorInterceptor = (error: any) => any | Promise<any>;

class RequestManager {
	private baseURL: string = '';
	private defaultConfig: Partial<RequestConfig> = {
		timeout: 10000,
		showLoading: false,
		showError: true,
		needAuth: true,
		retry: 0,
	};

	// 拦截器
	private requestInterceptors: RequestInterceptor[] = [];
	private responseInterceptors: ResponseInterceptor[] = [];
	private errorInterceptors: ErrorInterceptor[] = [];

	// 请求队列（用于管理并发）
	private requestQueue: Map<string, any> = new Map();

	constructor(baseURL?: string) {
		if (baseURL) {
			this.baseURL = baseURL;
		}
		this.setupDefaultInterceptors();
	}

	/**
	 * 设置基础URL
	 */
	setBaseURL(url: string): void {
		this.baseURL = url;
	}

	/**
	 * 设置默认配置
	 */
	setDefaultConfig(config: Partial<RequestConfig>): void {
		this.defaultConfig = { ...this.defaultConfig, ...config };
	}

	/**
	 * 添加请求拦截器
	 */
	addRequestInterceptor(interceptor: RequestInterceptor): void {
		this.requestInterceptors.push(interceptor);
	}

	/**
	 * 添加响应拦截器
	 */
	addResponseInterceptor(interceptor: ResponseInterceptor): void {
		this.responseInterceptors.push(interceptor);
	}

	/**
	 * 添加错误拦截器
	 */
	addErrorInterceptor(interceptor: ErrorInterceptor): void {
		this.errorInterceptors.push(interceptor);
	}

	/**
	 * 设置默认拦截器
	 */
	private setupDefaultInterceptors(): void {
		// 请求拦截器 - 添加认证头
		this.addRequestInterceptor((config) => {
			if (config.needAuth) {
				const authStore = useAuthStore();
				if (authStore.token) {
					config.header = {
						...config.header,
						accessToken: `${authStore.token}`,
					};
				}
			}
			return config;
		});

		// 请求拦截器 - 添加通用头部
		this.addRequestInterceptor((config) => {
			config.header = {
				'Content-Type': 'application/json',
				'X-Platform': this.getPlatform(),
				...config.header,
			};
			return config;
		});

		// 响应拦截器 - 统一处理响应格式
		this.addResponseInterceptor((response) => {
			const { data, statusCode } = response;
			if (statusCode === 200) {
				// 根据后端约定的格式处理
				if (data && typeof data === 'object') {
					if (data.ret === 0 || data.info === 'success') {
						return data.body || data;
					} else {
						throw new Error(data.message || '请求失败');
					}
				}
				return data;
			} else {
				throw new Error(`HTTP ${statusCode}: ${this.getStatusText(statusCode)}`);
			}
		});

		// 错误拦截器 - 处理认证错误
		this.addErrorInterceptor(async (error) => {
			if (error.statusCode === 401) {
				const authStore = useAuthStore();
				authStore.clearToken();

				// 跳转到登录页
				Taro.reLaunch({
					url: '/pages/user/mine/index',
				});

				throw new Error('登录已过期，请重新登录');
			}
			throw error;
		});
	}

	/**
	 * 获取平台信息
	 */
	private getPlatform(): string {
		const env = Taro.getEnv();
		switch (env) {
			case Taro.ENV_TYPE.WEAPP:
				return 'weapp';
			case Taro.ENV_TYPE.WEB:
				return 'h5';
			case Taro.ENV_TYPE.RN:
				return 'rn';
			default:
				return 'unknown';
		}
	}

	/**
	 * 获取状态码描述
	 */
	private getStatusText(statusCode: number): string {
		const statusMap: Record<number, string> = {
			400: '请求参数错误',
			401: '未授权',
			403: '禁止访问',
			404: '请求地址不存在',
			405: '请求方法不允许',
			408: '请求超时',
			500: '服务器内部错误',
			502: '网关错误',
			503: '服务不可用',
			504: '网关超时',
		};
		return statusMap[statusCode] || '网络错误';
	}

	/**
	 * 生成请求唯一标识
	 */
	private generateRequestKey(config: RequestConfig): string {
		const { url, method = 'GET', data } = config;
		return `${method}:${url}:${JSON.stringify(data || {})}`;
	}

	/**
	 * 执行请求拦截器
	 */
	private async executeRequestInterceptors(config: RequestConfig): Promise<RequestConfig> {
		let processedConfig = config;
		for (const interceptor of this.requestInterceptors) {
			processedConfig = await interceptor(processedConfig);
		}
		return processedConfig;
	}

	/**
	 * 执行响应拦截器
	 */
	private async executeResponseInterceptors(response: any): Promise<any> {
		let processedResponse = response;
		for (const interceptor of this.responseInterceptors) {
			processedResponse = await interceptor(processedResponse);
		}
		return processedResponse;
	}

	/**
	 * 执行错误拦截器
	 */
	private async executeErrorInterceptors(error: any): Promise<any> {
		let processedError = error;
		for (const interceptor of this.errorInterceptors) {
			try {
				processedError = await interceptor(processedError);
			} catch (e) {
				processedError = e;
			}
		}
		return processedError;
	}

	/**
	 * 显示加载提示
	 */
	private showLoading(text: string): void {
		Taro.showLoading({
			title: text,
			mask: true,
		});
	}

	/**
	 * 隐藏加载提示
	 */
	private hideLoading(): void {
		Taro.hideLoading();
	}

	/**
	 * 显示错误提示
	 */
	private showError(message: string): void {
		Taro.showToast({
			title: message,
			icon: 'error',
			duration: 2000,
		});
	}

	/**
	 * 核心请求方法
	 */
	async request<T = any>(config: RequestConfig): Promise<T> {
		// 合并配置
		const finalConfig: RequestConfig = {
			...this.defaultConfig,
			...config,
			url: config.url.startsWith('http') ? config.url : `${this.baseURL}${config.url}`,
		};

		// 生成请求标识
		const requestKey = this.generateRequestKey(finalConfig);

		// 防重复请求
		if (this.requestQueue.has(requestKey)) {
			return this.requestQueue.get(requestKey);
		}

		let loadingShown = false;

		try {
			// 执行请求拦截器
			const processedConfig = await this.executeRequestInterceptors(finalConfig);

			// 显示加载提示
			if (processedConfig.showLoading) {
				this.showLoading(processedConfig.loadingText || '加载中...');
				loadingShown = true;
			}

			// 创建请求Promise
			const requestPromise = this.executeRequest<T>(processedConfig);

			// 添加到请求队列
			this.requestQueue.set(requestKey, requestPromise);

			// 执行请求
			const response = await requestPromise;

			// 执行响应拦截器
			const processedResponse = await this.executeResponseInterceptors(response);

			return processedResponse;
		} catch (error) {
			// 执行错误拦截器
			const processedError = await this.executeErrorInterceptors(error);

			// 显示错误提示
			if (finalConfig.showError && processedError.message) {
				this.showError(processedError.message);
			}

			throw processedError;
		} finally {
			// 清理
			this.requestQueue.delete(requestKey);

			if (loadingShown) {
				this.hideLoading();
			}
		}
	}

	/**
	 * 执行实际请求
	 */
	private async executeRequest<T>(config: RequestConfig): Promise<T> {
		const { retry = 0 } = config;
		let lastError: any;

		for (let i = 0; i <= retry; i++) {
			try {
				const response = await Taro.request(config);
				return response as T;
			} catch (error) {
				lastError = error;

				// 如果是最后一次重试，直接抛出错误
				if (i === retry) {
					break;
				}

				// 等待一段时间后重试
				await new Promise((resolve) => setTimeout(resolve, 1000 * (i + 1)));
			}
		}

		throw lastError;
	}

	/**
	 * GET 请求
	 */
	get<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
		return this.request<T>({
			url,
			method: 'GET',
			data: params,
			...config,
		});
	}

	/**
	 * POST 请求
	 */
	post<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
		return this.request<T>({
			url,
			method: 'POST',
			data,
			...config,
		});
	}

	/**
	 * PUT 请求
	 */
	put<T = any>(url: string, data?: any, config?: Partial<RequestConfig>): Promise<T> {
		return this.request<T>({
			url,
			method: 'PUT',
			data,
			...config,
		});
	}

	/**
	 * DELETE 请求
	 */
	delete<T = any>(url: string, params?: any, config?: Partial<RequestConfig>): Promise<T> {
		return this.request<T>({
			url,
			method: 'DELETE',
			data: params,
			...config,
		});
	}
}

// 创建默认实例
const request = new RequestManager();

// 导出
export default request;
export { RequestManager };
