import Taro, { switchTab, showToast } from '@tarojs/taro';
import {
	jwCloudAuth_SecurityCenter_AppLogin_POST,
	jwCloudAuth_SecurityCenter_AppUserAddAndLogin_POST,
	jwCloudAuth_SecurityCenter_System_User_GetWebCurrentUserInfo_GET,
	jwCloudAuth_SecurityCenter_Quit_POST,
} from './api';
import { useAuthStore, useUserStore } from '@/stores';
import { saveStore } from '@/utils/utils';

// 微信手机号登录
export const onLoginHandler = async (e: any, urlPath: string = '/pages/mine/index') => {
	console.log('onLoginHandler', e);
	// 小程序登录的一些必要字段
	// encryptedData（手机号加密数据）, iv
	const { encryptedData, iv } = e.detail;
	// 只有允许获取手机号才继续执行
	if (e.detail.errMsg !== 'getPhoneNumber:ok') {
		return showToast({
			title: '当前小程序无获取手机号权限，请联系管理员',
			icon: 'none',
		});
	}
	// 获取用户信息
	// const userInfo = await Taro.getUserInfo();
	// console.log('userInfo:', userInfo);
	// const { avatarUrl } = userInfo.userInfo;

	// 初始化一个token字符串
	let token = '';
	try {
		// 获取code
		const { code } = await Taro.login();
		if (code) {
			const params = {
				code, // 小程序登录凭证
				appName: 'A能家园', // 小程序名称
				encryptedData: encryptedData, // 加密数据
				iv: iv, // 加密算法的初始向量
			};
			// 通过调用 后端与微信平台对接的接口来判断 用户是直接存在的还是需要新注册
			const wxLoginResStr: any = await jwCloudAuth_SecurityCenter_AppLogin_POST(params);
			console.log('wxLoginResStr', wxLoginResStr);
			// 返回的是字符串【注册过则Token，未注册则返回openid和session_key组成的字符串】
			// 从而通过是否包含token来判断是否登录成功
			const isToken = Boolean(wxLoginResStr?.includes('Bearer'));

			if (!isToken) {
				// 不存在该用户则需要调用新增用户接口
				const resObj = JSON.parse(wxLoginResStr); // 尝试解析JSON
				if (!resObj.openid || !resObj.session_key) {
					throw new Error('返回数据缺少必要字段');
				}
				const { openid, session_key } = resObj; // 从解析后的对象解构
				const params = {
					appId: 'wx66337e2d9551fbf2', // 小程序appId
					encryptedData, // 加密数据
					iv, // 加密算法的初始向量
					openid, // 微信返回的openid
					sessionKey: session_key, // 微信返回的sessionKey
					profilePicture: 'tmp/picture/tmp.png', // 先随便填个字符串，微信返回的用户头像
				};
				const newRegisterRes =
					await jwCloudAuth_SecurityCenter_AppUserAddAndLogin_POST(params);
				console.log('newRegisterRes:', newRegisterRes);
				token = newRegisterRes; // 新增用户登录成功后返回的token
				try {
				} catch (parseError) {
					console.error('JSON解析失败:', parseError);
					Taro.showToast({
						title: '登录数据解析失败',
						icon: 'none',
					});
					return; // 终止流程
				}
			} else {
				// 存在该用户则直接登录
				token = wxLoginResStr;
			}
			// 登录成功后，需要存储两遍
			// 1.存储到LocalStorage中，使用saveStore方法
			// 2.存储到pinia中，使用useAuthStore方法
			// 这样的好处是避免频繁读写LocalStorage
			// 3.刷新或者重新打开小程序时，读取LocalStorage的数据赋值到pinia中
			saveStore('token', token);
			const authStore = useAuthStore();
			authStore.setToken(token);
			// 登录成功后，将token存储到本地
			const userInfoRes =
				await jwCloudAuth_SecurityCenter_System_User_GetWebCurrentUserInfo_GET();
			const userStore = useUserStore();
			userStore.setUserInfo(userInfoRes);
			saveStore('userInfo', userInfoRes);
			switchTab({ url: urlPath });
		}
	} catch (error) {
		console.log('login error:', error);
		Taro.showToast({
			title: error.errMsg,
			icon: 'none',
		});
	}
};

// 退出登录
export const onLogoutHandler = async () => {
	try {
		// 退出登录
		await jwCloudAuth_SecurityCenter_Quit_POST();
	} catch (error) {
		Taro.showToast({
			title: '退出登录失败',
			icon: 'none',
		});
	} finally {
		const authStore = useAuthStore();
		authStore.setToken('');
		const userStore = useUserStore();
		userStore.setUserInfo(null);
		saveStore('token', '');
		saveStore('userInfo', null);
		Taro.reLaunch({ url: '/pages/login/index' });
	}
};
