import {
	clearStorageSync,
	getStorageSync,
	reLaunch,
	removeStorageSync,
	setStorageSync,
} from '@tarojs/taro';
import { useAuthStore } from '@/stores';
import Taro from '@tarojs/taro';
import { getApiConfig } from '@/config/api';
/**
 *
 * @param {string} key
 * @param {string} data
 * @returns {void}
 */
export const saveStore = (key, data) => {
	return setStorageSync(key, data);
};
/**
 *
 * @param {string} key
 * @returns {void}
 */
export const removeStore = (key) => {
	return removeStorageSync(key);
};
/**
 *
 * @param {string} key
 * @returns {string}
 */
export const getStore = (key) => {
	return getStorageSync(key);
};
/**
 *
 * @param {string} key
 * @returns {void}
 */
export const clearStore = () => {
	return clearStorageSync();
};

/**
 *
 * @param {string} message 需要发送的消息
 * @param {string} key key,可以为空,如果含有key则尝试进行结构化.如果原先为字符串,因为多次结构化请注意数据格式
 */
export const postNativeMessage = (message: string, key = '') => {
	const native: any = window;
	let sendMessage = '';
	if (!key) {
		sendMessage = message;
	} else {
		sendMessage = JSON.stringify({ [key]: message });
	}
	native.postMessage(sendMessage);
};

export const checkUser = () => {
	//针对不存在userInfo的情况下,跳转到登陆页面
	const userInfo = getStore('userInfo');
	if (!userInfo) {
		//不存在,进行跳转
		reLaunch({ url: '/pages/login' });
		return false;
		// postNativeMessage('鉴权信息丢失,跳转到登录页', 'errorInfo');
	}
	return true;
};

export const userLogout = () => {
	const authStore = useAuthStore();
	authStore.$reset(); //重置存在pinia的信息
	clearStore(); //清理用户信息
	reLaunch({ url: '/pages/mine/index' });
};

let timer;
/**
 * 特殊防抖，采用节流方式先执行，然后禁止durationTime秒
 * @param fn
 * @param durationTime
 * @returns
 */
export const debounce = (fn, durationTime = 300) => {
	if (timer) return;
	fn.call();
	timer = setTimeout(() => {
		clearTimeout(timer);
		timer = null;
	}, durationTime);
};
// 区分iOS、Android还是Browser
export const getDeviceInfo = () => {
	let userAgent = window.navigator.userAgent;
	var isAndroid = userAgent.indexOf('Android') > -1 || userAgent.indexOf('Adr') > -1; // Android终端
	var isiOS = !!userAgent.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // iOS终端
	if (isAndroid) {
		return 'Android';
	} else if (isiOS) {
		return 'iOS';
	} else {
		return 'Browser';
	}
};

// 检查Token
export const checkedToken = () => {
	const authStore = useAuthStore();
	if (authStore.token) {
		return true;
	} else {
		return false;
	}
};

// 缺token导致的退出登录
export const missingTokenandLogout = () => {
	// token不存在退出登录 - 第一次获取token的方法会塞入一个tempToken以示区分
	saveStore('logout', true);
	reLaunch({ url: '/pages/login' });
};

// 图片base64转换
export const convertToBase64 = (filePath) => {
	const fs = Taro.getFileSystemManager();
	return new Promise((resolve, reject) => {
		fs.readFile({
			filePath,
			encoding: 'base64',
			success(res) {
				const base64 = 'data:image/jpeg;base64,' + res.data;
				resolve(base64);
			},
			fail(err) {
				reject(err);
			},
		});
	});
};

// 获取完整的图片地址
export const getFullAvatarUrl = (url: string) => {
	return url.startsWith('http') ? url : `${getApiConfig().baseURL}${url}`;
};
