import http from '@/libs/request';
import Taro from '@tarojs/taro';
import { getApiConfig } from '@/config/api';

// 登录
export const jwCloudAuth_SecurityCenter_AppLogin_POST = (params) =>
	http.post(`/jw-cloud-auth/security-center/appLogin`, params, {
		header: {
			'Content-Type': 'application/json;charset=UTF-8',
		},
		needAuth: false,
	});

//登录用户新增
export const jwCloudAuth_SecurityCenter_AppUserAddAndLogin_POST = (params) =>
	http.post(`/jw-cloud-auth/security-center/appUserAddAndLogin`, params, {
		header: {
			'Content-Type': 'application/json;charset=UTF-8',
		},
		needAuth: false,
	});

// 获取用户信息
export const jwCloudAuth_SecurityCenter_System_User_GetWebCurrentUserInfo_GET = () =>
	http.get(`/jw-cloud-auth/security-center/system/user/getWebCurrentUserInfo`, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});

// 退出登录
export const jwCloudAuth_SecurityCenter_Quit_POST = () => {
	return http.post(`/jw-cloud-auth/security-center/quit`);
};

// 修改用户信息
export const jwCloudAuth_SecurityCenter_System_User_AppUserEdit_POST = (body) => {
	return http.post(`/jw-cloud-auth/security-center/system/user/appUserEdit`, body, {
		header: {
			'Content-Type': 'application/x-www-form-urlencoded',
		},
	});
};

/**
 * 上传文件 - 使用 FormData 方式
 * @param file 文件对象或文件路径
 * @param path 可选的文件路径参数
 */
export const jwCloudPedestal_V1_Fastdfs_UploadFile_POST = (file: string | File, path?: string) => {
	return new Promise((resolve, reject) => {
		// 获取认证 token
		import('@/stores').then(({ useAuthStore }) => {
			const authStore = useAuthStore();

			// 使用 Taro.uploadFile 进行文件上传
			Taro.uploadFile({
				url: `${getApiConfig().baseURL}/jw-cloud-pedestal/v1/fastdfs/uploadFile`,
				filePath: typeof file === 'string' ? file : '', // 如果是文件路径
				name: 'file', // 后端接收的字段名
				formData: {
					// 可选的 path 参数
					...(path && { path }),
				},
				header: {
					// 添加认证头部
					accessToken: authStore.token || '',
					// 不需要手动设置 Content-Type，Taro 会自动设置为 multipart/form-data
				},
				success: (res) => {
					try {
						const data = JSON.parse(res.data);
						if (data.ret === 0 || data.info === 'success') {
							resolve(data.body || data);
						} else {
							reject(new Error(data.message || '上传失败'));
						}
					} catch (error) {
						reject(new Error('上传失败'));
					}
				},
				fail: (error) => {
					reject(error);
				},
			});
		});
	});
};

/**
 * 上传 base64 图片文件
 * @param base64Data base64 图片数据
 * @param path 可选的文件路径参数
 */
// export const jwCloudPedestal_V1_Fastdfs_UploadBase64Image_POST = (base64Data: string, path?: string) => {
// 	const body = {
// 		base64Data,
// 		// 只有 path 不为空时才添加到 body 中
// 		...(path && path !== '' && { path }),
// 	};

// 	return http.post(
// 		`/jw-cloud-pedestal/v1/fastdfs/uploadBase64`,
// 		body,
// 		{
// 			header: {
// 				'Content-Type': 'application/json',
// 			},
// 		}
// 	);
// };

/**
 * 通过 URL 下载并上传文件
 * @param imageUrl 图片 URL（如微信头像 URL）
 * @param path 可选的文件路径参数
 */
export const downloadAndUploadImage = async (imageUrl: string, path?: string) => {
	try {
		// 第一步：下载图片到本地临时文件
		const downloadResult = await new Promise<string>((resolve, reject) => {
			Taro.downloadFile({
				url: imageUrl,
				success: (res) => {
					if (res.statusCode === 200) {
						resolve(res.tempFilePath);
					} else {
						reject(new Error(`下载失败，状态码：${res.statusCode}`));
					}
				},
				fail: (error) => {
					reject(new Error(`下载失败：${error.errMsg}`));
				},
			});
		});

		// 第二步：上传下载的临时文件
		const uploadResult = await jwCloudPedestal_V1_Fastdfs_UploadFile_POST(downloadResult, path);
		return uploadResult;
	} catch (error) {
		console.error('下载并上传图片失败:', error);
		throw error;
	}
};

/**
 * 微信头像上传的便捷方法
 * @param avatarUrl 微信头像 URL
 */
export const uploadWechatAvatar = async (avatarUrl: string) => {
	return downloadAndUploadImage(avatarUrl, 'avatars/');
};
