{"name": "taro_community", "version": "1.0.0", "private": true, "description": "园区项目微信小程序", "templateInfo": {"name": "vue3-NutUI", "typescript": true, "css": "Sass", "framework": "Vue3"}, "scripts": {"build:weapp": "cross-env NODE_ENV=production TARO_ENV=weapp taro build --type weapp", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "dev:weapp": "cross-env NODE_ENV=development TARO_ENV=weapp taro build --type weapp --watch", "min:dev:weapp": "cross-env NODE_ENV=production TARO_ENV=weapp taro build --type weapp --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "prepare": "husky install"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "license": "MIT", "dependencies": {"@babel/runtime": "^7.7.7", "@nutui/icons-vue-taro": "^0.0.9", "@nutui/nutui-taro": "^4.2.8", "@tarojs/components": "4.1.2", "@tarojs/helper": "4.1.2", "@tarojs/plugin-framework-vue3": "4.1.2", "@tarojs/plugin-html": "4.1.2", "@tarojs/plugin-platform-alipay": "4.1.2", "@tarojs/plugin-platform-h5": "4.1.2", "@tarojs/plugin-platform-jd": "4.1.2", "@tarojs/plugin-platform-qq": "4.1.2", "@tarojs/plugin-platform-swan": "4.1.2", "@tarojs/plugin-platform-tt": "4.1.2", "@tarojs/plugin-platform-weapp": "4.1.2", "@tarojs/runtime": "4.1.2", "@tarojs/shared": "4.1.2", "@tarojs/taro": "4.1.2", "pinia": "^3.0.3", "vue": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@nutui/auto-import-resolver": "^1.0.0", "@tarojs/cli": "4.1.2", "@tarojs/taro-loader": "4.1.2", "@tarojs/webpack5-runner": "4.1.2", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/compiler-sfc": "^3.0.0", "babel-preset-taro": "4.1.2", "cross-env": "^7.0.3", "css-loader": "3.4.2", "eslint": "^8.12.0", "eslint-config-taro": "4.1.2", "eslint-plugin-vue": "^8.0.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.4.18", "prettier": "^3.5.3", "prettier-eslint": "^16.4.2", "prettier-standard": "^16.4.1", "style-loader": "1.3.0", "stylelint": "^14.4.0", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.1.0", "unplugin-vue-components": "^0.26.0", "vue-loader": "^17.0.0", "webpack": "5.91.0"}, "lint-staged": {"*.{vue,js,ts,jsx,tsx,json}": "prettier --write"}}